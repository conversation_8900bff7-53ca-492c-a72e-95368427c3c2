{"name": "standalone-site-frontend", "description": "Standalone Site", "scripts": {"dev": "cross-env pnpm clean:nm && cross-env pnpm install && cross-env pnpm --parallel --filter './packages/**' dev", "build": "cross-env pnpm --parallel --filter './packages/**' build", "build:loc": "cross-env pnpm --parallel --filter './packages/**' build:loc", "build:test": "cross-env  pnpm --parallel --filter './packages/**' build:test", "build:pre": "cross-env pnpm --parallel --filter './packages/**' build:pre", "build:prod": "cross-env pnpm --parallel --filter './packages/**' build:prod", "preview": "cross-env pnpm --parallel --filter './packages/**' preview", "format": "cross-env pnpm --parallel --filter './packages/**' format ", "release": "cross-env zx ./scripts/release.mjs", "prepare": "husky install", "clean:nm": "cross-env node ./scripts/clean-nm.mjs", "i18n": "cross-env zx ./scripts/i18n.mjs", "i18n:update": "tsx ./scripts/i18n.config.update.ts", "bump": "cross-env zx ./scripts/version.mjs", "image": "cross-env zx ./scripts/image.mjs", "lighthouse": "cross-env node ./scripts/lighthouse/index.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.658.0", "@intlsdk/account-api": "^1.20.2", "@lhlyu/vue-virtual-waterfall": "^1.0.6", "@tencent/pa-cms-utils": "^0.3.25", "@tencent/pa-ingame-utils": "^0.1.2", "@tencent/pa-share-utils": "^0.0.6", "@vueuse/motion": "^2.2.3", "axios": "^1.3.5", "cos-js-sdk-v5": "^1.8.4", "dayjs": "^1.11.7", "dompurify": "^3.0.0", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "p-limit": "^6.2.0", "postcss-100vh-fix": "^1.0.2", "query-string": "^9.1.0", "radix-vue": "^1.9.5", "sharp": "^0.33.5", "tailwindcss-animate": "^1.0.7", "tsx": "^4.7.0", "uuid": "^10.0.0", "workbox-cacheable-response": "^7.3.0", "workbox-core": "^7.3.0", "workbox-expiration": "^7.3.0", "workbox-navigation-preload": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0", "workbox-window": "^7.3.0", "vue": "^3.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@commitlint/cli": "^17.6.1", "@commitlint/config-conventional": "^17.6.1", "@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-libvips-darwin-arm64": "^1.0.6", "@img/sharp-libvips-linux-x64": "^1.0.6", "@img/sharp-libvips-linuxmusl-x64": "^1.0.6", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-linuxmusl-x64": "^0.33.5", "@intlify/unplugin-vue-i18n": "^0.10.0", "@rollup/plugin-multi-entry": "^6.0.1", "@rollup/plugin-typescript": "^11.1.0", "@types/csso": "^5.0.4", "@types/dompurify": "^3.0.5", "@types/js-cookie": "^3.0.3", "@types/lodash-es": "^4.17.7", "@types/node": "^18.15.11", "@types/uuid": "^10.0.0", "@typescript-eslint/parser": "^5.59.0", "autoprefixer": "^10.4.14", "chalk": "^5.3.0", "chokidar": "^3.6.0", "commitlint": "^17.6.1", "cross-env": "^7.0.3", "csso": "^5.0.5", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-define-config": "^1.18.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.11.0", "fast-glob": "^3.3.2", "husky": "^8.0.0", "less": "^4.1.3", "lighthouse": "^10.1.0", "lint-staged": "^13.2.1", "postcss": "^8.4.22", "postcss-html": "^1.5.0", "postcss-import": "^16.1.0", "postcss-rtl": "^2.0.0", "prettier": "^2.8.7", "puppeteer": "^19.8.2", "rollup-plugin-critical": "^1.0.14", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.62.0", "tailwindcss": "^3.4.10", "typescript": "^5.0.4", "vconsole": "^3.15.0", "vite": "5.0.0", "vite-plugin-chunk-split": "^0.5.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-preload": "^0.4.2", "vite-plugin-pwa": "^1.0.0", "zx": "^7.2.1"}, "keywords": [], "author": "", "license": "ISC", "engines": {"node": ">=v18.20.0", "pnpm": ">=8.15.5", "npm": "please-use-pnpm", "yarn": "please-use-pnpm"}, "packageManager": "pnpm@8.15.5", "pnpm": {"supportedArchitectures": {"os": ["win32", "darwin", "current"], "cpu": ["x64", "arm64"]}}, "version": "1.6.0-v15"}