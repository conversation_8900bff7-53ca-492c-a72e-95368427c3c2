<template>
  <div class="min-h-screen w-full pb-[30px]" :class="{ 'year2-5': anniversary }">
    <Nav v-if="show_child" :is-anniversary="anniversary"></Nav>
    <Banner v-if="show_child" :list="bannerList"></Banner>

    <div class="tabs-content">
      <div
        v-sticky="{
          sticky_position: 44,
          sticky_z_index: 20,
          validate: () => true,
          callback: (bool: boolean) => (is_nav_sticky = bool),
          observer_options: {
            threshold: 0.2,
          },
        }"
        :class="[
          `tabs-content-hd flex z-20 left-0 items-center h-[39px] -mt-[19px]`,
          is_nav_sticky && `fixed-nav`,
        ]"
      >
        <TabScroll
          v-if="tabList.length > 0"
          class="tab-scroll relative z-[2] rounded-[16px_16px_0_0]"
          :tabs="tabList"
          :active_id="activeId"
          @change="onChangeTab"
        ></TabScroll>
      </div>

      <template v-if="tabList.length > 0">
        <div
          v-if="anniversary && isMobile"
          ref="refFixBg"
          class="max-width: var(--max-pc-w);"
        ></div>
        <div ref="tabsContentBd" class="tabs-content-bd bg-img">
          <component :is="active_component"></component>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onActivated, onDeactivated, watch } from "vue";
import { storeToRefs } from "pinia";
import Nav from "@/components/home/<USER>/index.vue";
import Banner from "@/components/home/<USER>/index.vue";
import Recommend from "@/components/home/<USER>/index.vue";
import Outpost from "@/components/home/<USER>/index.vue";
import Guides from "@/components/home/<USER>/index.vue";
import NikkeArt from "@/components/home/<USER>/index.vue";
import CreatorHub from "@/components/home/<USER>/index.vue";
import Official from "@/components/home/<USER>/index.vue";
import TabEvent from "@/components/home/<USER>/index.vue";
import TabScroll from "@/components/common/tabs/tab-scroll.vue";
import { computed } from "vue";
import { useHomeStore } from "@/store/home/<USER>";
import { useAnniversary } from "@/store/home/<USER>";
import { report } from "packages/utils/tlog.ts";
import { useSetting } from "@/store/setting.ts";

import { event_emitter, EVENT_NAMES } from "packages/utils/event-emitter";
import { PlatId } from "packages/types/common";

const { getConfig, getBanners, changeTab } = useHomeStore();
const { activeId, activeKey, tabList, bannerList } = storeToRefs(useHomeStore());
const { anniversary } = storeToRefs(useAnniversary());
const { is_unread } = storeToRefs(useSetting());
const is_nav_sticky = ref(false);

const active_component = computed(() => {
  return (
    {
      recommend: Recommend,
      outpost: Outpost,
      nikkeart: NikkeArt,
      guides: Guides,
      creatorhub: CreatorHub,
      event: TabEvent,
      official: Official,
    }[activeKey.value] || Outpost
  );
});

watch(
  activeKey,
  () => {
    if (!activeKey.value) return;
    report.standalonesite_sub_tab_page.cm_vshow({
      label_id: +activeId.value,
      label_name: tabList.value.find((item) => item.value === activeId.value)?.label!,
    });
  },
  { immediate: true },
);

const onChangeTab = (id: string | number) => {
  const target = tabList.value.find((item) => item.value === id);

  if (id === activeId.value) {
    refreshCurrentTab(id);
  }

  changeTab(id as string);

  // tab上报
  report.standalonesite_sub_tab_btn.cm_click({
    label_id: +id,
    label_name: target?.label!,
  });
};

const refreshCurrentTab = (id: string | number) => {
  if (!id) return;
  const tab = tabList.value.find((item) => item.value === id);
  if (!tab) return;

  event_emitter.emit(EVENT_NAMES.refresh_home_plate_post_list, tab.key as PlatId);
};

const show_child = ref(true); // 控制子组件的重新渲染
const tabsContentBd = ref();
const refFixBg = ref();

const heartbeatId = ref(); // 心跳检测id
const startTime = ref(0); // 当前时间戳

const isMobile = computed(() => window.innerWidth <= 768);

const onStopHeartbeat = () => {
  if (heartbeatId.value) {
    clearInterval(heartbeatId.value);
    heartbeatId.value = null;
  }
};

onActivated(() => {
  startTime.value = Date.now();
  show_child.value = true;

  onStopHeartbeat();

  // 每隔10秒上报停留时长
  heartbeatId.value = setInterval(() => {
    report.standalonesite_heart_beat.cm_click();
  }, 10 * 1000);

  report.standalonesite_home_page.cm_vshow({ red_tag: is_unread.value }); // 等待页面加载完成上报
  setTimeout(() => {
    report.standalonesite_realfinish_page.cm_vshow({ red_tag: is_unread.value });
  }, 2000);
});

onDeactivated(() => {
  onStopHeartbeat();
  const stayTime = Date.now() - startTime.value;
  report.standalonesite_home_stay.cm_lvtm({ du: stayTime });

  show_child.value = false;
});

onMounted(() => {
  getBanners();
  getConfig();
});

defineOptions({
  name: "HOME", // 定义组件名称
});
</script>

<style scoped lang="css">
@-moz-document url-prefix() {
  .tab-scroll {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
  }
}
</style>
