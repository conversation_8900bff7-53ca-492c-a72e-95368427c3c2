<template>
  <div
    class="w-full min-h-screen bg-[color:var(--fill-3)] mx-auto pt-[44px] pb-[30px] box-border relative z-[1]"
  >
    <Head
      :title="t(is_self ? 'following' : 'his_following')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      @goback="handleRouterBack"
    ></Head>
    <InfiniteScroll
      :distance="100"
      :back_to_top_visible="false"
      :loading_visible="false"
      :finished_visible="false"
      :loading="loading"
      :empty="empty"
      :finished="finished"
      :debounce_interval="10"
      @load-more="load"
    >
      <ul>
        <li v-for="(item, index) in list" :key="`following_` + index" class="mt-[12px]">
          <FansItem :data="item" @follow="reset"></FansItem>
        </li>
      </ul>
    </InfiniteScroll>
    <div class="absolute bottom-[7px] left-0 w-full h-[235px] rotate-180 -z-[1]">
      <div class="absolute w-full top-0 h-[235px] left-0 -z-[1] pointer-events-none">
        <SvgIcon name="bg-mask" color="var(--line-1-40)"></SvgIcon>
      </div>
      <i
        class="absolute bottom-0 left-0 w-full h-[235px] z-[-1] bg-[image:var(--linear-gradient-2)] pointer-events-none"
      ></i>
    </div>
  </div>
</template>

<script setup lang="ts">
import { InfiniteScroll } from "@/components/common/scroll/index";
import Head from "@/components/common/head/index.vue";
import FansItem from "@/components/common/fans-item/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { useRouter } from "vue-router";

import { useGetUserFollow } from "@/api/user";
import { useUserCenter } from "@/composables/use-user-center";
import { t } from "@/locales";
import { useTanstackInfiniteList } from "@/composables/use-tanstack-infinite-list";
import { computed } from "vue";

const router = useRouter();
const handleRouterBack = () => {
  router.back();
};

const { user_info, is_self } = useUserCenter();

const { empty, finished, list, load, loading, reset } = useTanstackInfiniteList({
  queryKey: ["following", user_info.value?.intl_openid],
  queryFn: (next_page_cursor) => {
    return useGetUserFollow.run({
      page_type: 0,
      limit: 10,
      next_page_cursor,
      intl_openid: user_info.value?.intl_openid ?? "",
    });
  },
  item_key: "id",
  staleTime: 1000 * 10,
  gcTime: 1000 * 15,
  enabled: computed(() => {
    return !!user_info.value?.intl_openid;
  }),
});
</script>
