<template>
  <div class="w-full min-h-screen bg-[color:var(--fill-3)] mx-auto pt-[44px] pb-[30px] box-border">
    <Head
      :title="t(is_self ? 'follower' : 'his_follower')"
      bg="bg-[var(--fill-0)]"
      color="var(--text-1)"
      @goback="handleRouterBack"
    ></Head>

    <InfiniteScroll
      :distance="100"
      :back_to_top_visible="false"
      :loading_visible="false"
      :finished_visible="false"
      :loading="loading"
      :empty="empty"
      :finished="finished"
      :debounce_interval="10"
      @load-more="load"
    >
      <ul>
        <li v-for="(item, index) in list" :key="`follower_` + index" class="mt-[12px]">
          <FansItem :data="item" @follow="reset"></FansItem>
        </li>
      </ul>
    </InfiniteScroll>
  </div>
</template>

<script setup lang="ts">
import { InfiniteScroll } from "@/components/common/scroll/index";
import Head from "@/components/common/head/index.vue";
import FansItem from "@/components/common/fans-item/index.vue";
import { useRouter } from "vue-router";
import { useGetUserFans } from "@/api/user";
import { computed } from "vue";
import { useUserCenter } from "@/composables/use-user-center";
import { t } from "@/locales";
import { useTanstackInfiniteList } from "@/composables/use-tanstack-infinite-list";

const router = useRouter();
const handleRouterBack = () => {
  router.back();
};

const { user_info, is_self } = useUserCenter();

const { empty, finished, list, load, loading, reset } = useTanstackInfiniteList({
  queryKey: ["follower", user_info.value?.intl_openid],
  queryFn: async (next_page_cursor) => {
    return useGetUserFans.run({
      page_type: 0,
      limit: window.innerHeight < 800 ? 10 : 20,
      next_page_cursor,
      intl_openid: user_info.value?.intl_openid ?? "",
    });
  },
  item_key: "id",
  enabled: computed(() => {
    return !!user_info.value?.intl_openid;
  }),
});
</script>
