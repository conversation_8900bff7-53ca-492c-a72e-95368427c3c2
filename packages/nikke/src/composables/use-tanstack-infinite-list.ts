// hooks/useTanstackInfiniteList.ts
import { computed, onActivated, onMounted, toValue, watch } from "vue";
import { useInfiniteQuery, useQueryClient, type QueryKey } from "@tanstack/vue-query";
import type { MaybeRef, MaybeRefOrGetter } from "vue";
import { useRoute } from "vue-router";

export type It<PERSON><PERSON><PERSON><T extends Record<string, any>> = {
  [key in keyof T]: T[key] extends string | number ? key : never;
}[keyof T];

export type SupportedPageResult<T extends Record<string, any>> =
  | PageResultWithPageInfo<T>
  | PageResultWithTotal<T>;

export type PageResultWithTotal<T extends Record<string, any>> = {
  list: T[];
  total?: number;
  is_finish: boolean;
};

export type PageResultWithPageInfo<T extends Record<string, any>> = {
  list: T[];
  page_info: {
    previous_page_cursor?: string;
    next_page_cursor?: string;
    is_finish: boolean;
  };
};

export const resolvePageResult = <T extends Record<string, any>>(
  res: SupportedPageResult<T>,
): {
  list: T[];
  total?: number;
  is_finish?: boolean;
  next_page_cursor?: string;
  previous_page_cursor?: string;
} => {
  if ("page_info" in res) {
    let is_finish = res.page_info.is_finish;
    if (
      res.list.length === 0 &&
      !res.page_info.next_page_cursor &&
      !res.page_info.previous_page_cursor
    ) {
      is_finish = true;
    }
    return {
      list: res.list,
      is_finish: is_finish,
      next_page_cursor: res.page_info.next_page_cursor,
      previous_page_cursor: res.page_info.previous_page_cursor,
    };
  }

  if ("total" in res || "is_finish" in res) {
    return {
      list: res.list,
      total: res.total,
      is_finish: res.is_finish,
    };
  }

  console.log("unsupported page format", res);
  throw new Error("unsupported page format");
};

export const useTanstackInfiniteList = <T extends Record<string, any>>(config: {
  /**
   * TanStack Query 的唯一键，必须是响应式的。
   * 例如：['myCollectionPostList', intl_openid]
   */
  queryKey: MaybeRef<QueryKey>;
  /**
   * 数据请求函数。
   * @param pageParam - 上一页返回的 next_page_cursor，第一页为 undefined
   */
  queryFn: (next_page_cursor: string | undefined) => Promise<SupportedPageResult<T>>;
  /**
   * 用于去重的唯一标识符
   */
  item_key: ItemKey<T> | ((item: T) => string | number);
  /**
   * 是否去重, 默认 true
   */
  remove_duplicate?: boolean;
  staleTime?: number;
  gcTime?: number;
  /**
   * 是否立即加载, 默认 true
   */
  enabled?: MaybeRefOrGetter<boolean>;
}) => {
  const queryClient = useQueryClient();

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isLoading,
    isFetching,
    isFetchingNextPage,
    refetch,
    isError,
    error,
  } = useInfiniteQuery({
    queryKey: config.queryKey,

    queryFn: ({ pageParam }) => {
      return config.queryFn(pageParam as string | undefined);
    },

    getNextPageParam: (lastPage) => {
      const resolved = resolvePageResult(lastPage);
      // 如果 is_finish 为 true 或没有 next_cursor，返回 undefined 表示没有下一页
      return resolved.is_finish ? undefined : resolved.next_page_cursor;
    },

    initialPageParam: undefined as string | undefined, // 第一页没有 cursor

    staleTime: config.staleTime,
    gcTime: config.gcTime,

    // 禁用内置的自动刷新，手动控制
    enabled: false,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });

  const getItemKey = (item: T) => {
    if (typeof config.item_key === "function") {
      return config.item_key(item);
    } else {
      return item[config.item_key] as string | number;
    }
  };

  // 将分页数据扁平化为单一列表
  const list = computed<T[]>(() => {
    const pages = data.value?.pages ?? [];
    const flatList = pages.flatMap((page) => page.list);

    if (config.remove_duplicate ?? true) {
      const item_key_set = new Set<string | number>();
      const uniqueList: T[] = [];
      flatList.forEach((item) => {
        const key = getItemKey(item);
        if (!item_key_set.has(key)) {
          uniqueList.push(item);
          item_key_set.add(key);
        }
      });
      return uniqueList;
    }

    return flatList;
  });

  const loading = computed(() => isLoading.value || isFetchingNextPage.value);

  const finished = computed(() => !hasNextPage.value && !isLoading.value);

  const empty = computed(() => list.value.length === 0 && !loading.value);

  const load = () => {
    if (hasNextPage.value && !isFetchingNextPage.value) {
      fetchNextPage();
    }
  };

  const reset = () => {
    const old_data = queryClient.getQueryData(toValue(config.queryKey)) as
      | { pages: SupportedPageResult<T>[]; pageParams: (string | undefined)[] }
      | undefined;
    const has_old_data = old_data?.pages?.length ?? 0 > 0;

    queryClient.removeQueries({ queryKey: toValue(config.queryKey) });
    // 强制先设置第一页的旧数据
    if (has_old_data) {
      queryClient.setQueryData(toValue(config.queryKey), {
        pages: old_data?.pages.slice(0, 1) ?? [],
        pageParams: [undefined],
      });
    }
    return refetch();
  };

  const enabled = computed(() => {
    return config.enabled ? toValue(config.enabled) : true;
  });

  const route = useRoute();
  const checkShouldReset = () => {
    const need_refresh = route.meta.needRefresh;
    // data is after gc
    const old_data = queryClient.getQueryData(toValue(config.queryKey));
    if ((need_refresh || !old_data) && enabled.value) {
      reset();
    }
  };

  watch(enabled, (new_value) => {
    if (new_value) reset();
  });

  // 在组件挂载或激活时执行决策
  onMounted(() => checkShouldReset());
  onActivated(() => checkShouldReset()); // 兼容 <KeepAlive>

  return {
    /** 扁平化后的数据列表 */
    list,
    /** 是否正在进行首次加载或加载下一页 */
    loading,
    /** 是否已加载完所有数据 */
    finished,
    /** 列表是否为空 (且不在加载中) */
    empty,
    /** 触发加载下一页的函数 */
    load,
    /** 重置并重新加载列表的函数 */
    reset,
    /** 原始的 TanStack Query 返回值，用于更高级的用法 */
    query: {
      data,
      isFetching,
      isError,
      error,
      refetch,
    },
  };
};
