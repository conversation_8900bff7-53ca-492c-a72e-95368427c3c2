import { computed, ref, toRaw, toValue, watch, type MaybeRefOrGetter } from "vue";
import { storeToRefs } from "pinia";
import { usePostList } from "@/api/post";
import { useHomeStore } from "@/store/home/<USER>";
import { useLanguageStore } from "@/store/language";
import { useTanstackInfiniteList } from "./use-tanstack-infinite-list";
import { dealPostData } from "@/utils/home";
import { getStandardizedGameId, getStandardizedLang } from "packages/utils/standard";
import { filterCmsItem } from "packages/utils/cms";
import type { PostItem, OrderBy } from "packages/types/post";

import { getTags, getDistrictList } from "@/api/home";
import { cloneDeep } from "lodash-es";
import { useSessionStorage } from "@vueuse/core";
import { usePostItem } from "./use-post";
import { PlatId } from "packages/types/common";
import { EVENT_NAMES, onEventEmitter } from "packages/utils/event-emitter";

export interface UseHomePlateListOptions {
  /**
   * 默认排序方式
   */
  defaultOrderBy?: OrderBy;
  /**
   * 是否启用金刚区功能
   */
  enableDistrict?: boolean;
  /**
   * 是否启用标签功能
   */
  enableTags?: boolean;
  /**
   * 是否立即加载数据
   */
  enabled?: MaybeRefOrGetter<boolean>;
  /**
   * 额外的请求参数
   */
  extraParams?: Record<string, any>;
}

/**
 * 首页板块列表通用 Composable
 * 统一管理帖子列表、标签、金刚区等功能
 */
export const useHomePlateList = (options: UseHomePlateListOptions = {}) => {
  const {
    defaultOrderBy = 2,
    enableDistrict = false,
    enableTags = true,
    enabled = true,
    extraParams = {},
  } = options;

  // 依赖的 store
  const { activeId: plateId, activeKey } = storeToRefs(useHomeStore());
  const { lang_regions } = storeToRefs(useLanguageStore());

  const { resolvePostItemImageInfo } = usePostItem();

  // 筛选状态
  const orderBy = useSessionStorage<OrderBy>(`orderBy-${activeKey.value}`, defaultOrderBy);
  const activeTag = useSessionStorage<string>(`activeTag-${activeKey.value}`, "");

  // 标签相关 - 使用 TanStack Query
  const { data: tagsData, isLoading: tagLoading } = getTags(
    computed(() => ({
      plate_id: Number(plateId.value),
      limit: 100,
    })),
    {
      enabled: computed(() => enableTags && !!plateId.value),
      staleTime: 1000 * 60 * 10, // 10分钟缓存
    },
  );

  const tagList = computed(() => {
    return (
      tagsData.value?.list.map((item) => ({
        id: item.id,
        tag_name: item.tag_name,
      })) || []
    );
  });

  // 金刚区相关 - 使用 TanStack Query
  const { data: districtData, isLoading: districtLoading } = getDistrictList(
    computed(() => ({
      plate_id: Number(plateId.value),
      limit: 10,
    })),
    {
      enabled: computed(() => enableDistrict && !!plateId.value),
      staleTime: 1000 * 60 * 10, // 10分钟缓存
    },
  );

  const districtList = computed(() => {
    if (!districtData.value?.list) return [];

    const game_id = getStandardizedGameId();
    const lang = getStandardizedLang() || "en";
    return [...districtData.value.list]
      .sort((x, y) => Number(x.order) - Number(y.order))
      .filter((item) => filterCmsItem({ ext_info: item.ext_info, lang, game_id }));
  });

  // 帖子列表
  const {
    list,
    loading: postLoading,
    finished: postFinished,
    empty: postEmpty,
    load: loadMore,
    reset: resetPostList,
  } = useTanstackInfiniteList<PostItem>({
    queryKey: computed(() => [
      "homePlatePostList",
      plateId.value,
      activeKey.value,
      orderBy.value,
      activeTag.value,
      lang_regions.value,
    ]),
    queryFn: async (next_page_cursor) => {
      const { list, page_info } = await usePostList.run({
        search_type: +activeTag.value ? 1 : 0, // 选择了tag，search_type=1
        plate_id: Number(plateId.value),
        plate_unique_id: activeKey.value,
        nextPageCursor: next_page_cursor,
        order_by: orderBy.value,
        tag_id: +activeTag.value || undefined,
        limit: "10",
        regions: lang_regions.value,
        ...extraParams, // 支持额外参数
      });

      // 处理数据
      const processedList =
        activeKey.value === PlatId.nikkeart
          ? await Promise.all(dealPostData(list).map(resolvePostItemImageInfo))
          : dealPostData(list);

      return {
        list: processedList,
        page_info,
      };
    },
    item_key: "post_uuid",
    enabled: computed(() => {
      const enabledValue = toValue(enabled);
      return enabledValue && !!plateId.value && !!activeKey.value;
    }),
  });

  /** 让帖子列表支持主动数据更新 */
  const postList = ref<PostItem[]>([]);

  watch(
    list,
    (newList) => {
      postList.value = cloneDeep(newList);
    },
    { immediate: true, deep: true },
  );

  // 切换排序
  const changeOrderBy = (newOrderBy: OrderBy) => {
    orderBy.value = newOrderBy;
    resetPostList();
  };

  // 切换标签
  const changeTag = (tagId: string) => {
    activeTag.value = tagId;
    resetPostList();
  };

  // 重置所有状态
  const reset = () => {
    activeTag.value = "";
    orderBy.value = defaultOrderBy;
    resetPostList();
  };

  const awaitPostListLoaded = async () => {
    if (postLoading.value) {
      await new Promise((resolve) => setTimeout(resolve, 20));
      return awaitPostListLoaded();
    } else {
      return;
    }
  };

  onEventEmitter(EVENT_NAMES.refresh_home_plate_post_list, (platId) => {
    if (platId === activeKey.value) {
      resetPostList();
    }
  });

  return {
    // 帖子列表相关
    postList,
    postLoading,
    postFinished,
    postEmpty,
    loadMore,
    resetPostList,

    // 筛选状态
    orderBy,
    activeTag,
    changeOrderBy,
    changeTag,

    // 标签相关
    tagList,
    tagLoading,

    // 金刚区相关
    districtList,
    districtLoading,

    // 通用方法
    awaitPostListLoaded,
    reset,
  };
};
