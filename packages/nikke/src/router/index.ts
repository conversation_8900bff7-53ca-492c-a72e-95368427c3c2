// types
import { KEEP_ALIVE_NAMES, Routes, routes, RoutesName } from "./routes";
import {
  createRouter,
  createWebHistory,
  NavigationFailure,
  RouteLocationNormalizedGeneric,
  RouteLocationRaw,
} from "vue-router";

// configs
import { STANDARD_GAME_ID_KEY } from "packages/configs/standard";

// utils
import { isEqual } from "lodash-es";
import { isGameLogin } from "packages/utils/tools";
import { EnsureType, To, useEnsureUrl } from "@/composables/use-ensure.ts";
import { base64Encode, isOriginalOpenId } from "packages/utils/encrypt";
import { COMMON_QUERY_KEYS } from "@/configs/const";
import logger from "packages/utils/logger";
import { getStandardizedCookie } from "packages/utils/standard";
import { STORAGE_COOKIE_GAME_ID } from "packages/configs/storage";
import { getToLoginQuery } from "packages/utils/login";
import { ref } from "vue";

const debug = logger("[router:logger]");
const { ensure } = useEnsureUrl();

const router = createRouter({
  history: createWebHistory(),
  routes,
});

const originRouterBack = router.back;
const originRouterPush = router.push;

let back_history_len = (() => {
  if (
    [
      1,
      //
      2,
    ].includes(history.length)
  ) {
    return 1;
  }
  return history.length;
})();

/** 当前是否正在回退 */
let is_back_pending = ref(false);

// debug.log(`back_history_len`, back_history_len, history.length);

router.push = async (to: RouteLocationRaw): Promise<NavigationFailure | void | undefined> => {
  back_history_len++;
  return originRouterPush(to);
};

router.back = () => {
  const { back } = router.options.history.state;
  debug.log(`back`, back);

  back_history_len--;

  if (back_history_len && back) {
    return originRouterBack();
  }

  // if no stack back, go home
  router.push({
    name: RoutesName.HOME,
  });
};

const checkUrlGameIdIsSameWithLsGameId = (options: {
  url_game_id?: string;
  cache_game_id: string;
}) => {
  const { url_game_id, cache_game_id } = options;

  if (url_game_id && cache_game_id) {
    return url_game_id === cache_game_id;
  }

  return true;
};

router.beforeEach((to: RouteLocationNormalizedGeneric, from, next) => {
  // if (to.name && KEEP_ALIVE_NAMES.includes(to.name as (typeof KEEP_ALIVE_NAMES)[number])) {
  //   if (!is_back_pending.value) {
  //     to.meta.needRefresh = true;
  //   }
  // }
  if (!is_back_pending.value) {
    to.meta.needRefresh = true;
  }
  is_back_pending.value = false;

  // must use spread operator
  const to_query = { ...to.query };
  const url_game_id = to_query[STANDARD_GAME_ID_KEY] as string;
  const cache_game_id = getStandardizedCookie(STORAGE_COOKIE_GAME_ID) as string;

  if (
    // 游戏内不需要操作
    !isGameLogin() &&
    !checkUrlGameIdIsSameWithLsGameId({ url_game_id, cache_game_id }) &&
    to.name !== RoutesName.LOGIN
  ) {
    // 手动更改了 game id 与当前登录的 game id 不一致，跳转到登录页
    return next({
      path: Routes.LOGIN,
      query: getToLoginQuery(to_query),
    });
  }

  ensure(EnsureType.router, to as To);

  const encrypt_querys = [COMMON_QUERY_KEYS.EncodedUid, COMMON_QUERY_KEYS.OpenId];

  // shiftyspad 客态状态保持
  if (to.matched.find((r) => r.path === Routes.SHIFTYSPAD)) {
    encrypt_querys.forEach((key) => {
      if (from.query?.[key] && to.query) to.query[key] = from.query[key];
    });
  }

  // 如果 query 中有 openid, 确保是 base64 格式
  encrypt_querys.forEach((query_need_hide) => {
    if (to.query?.[query_need_hide] && typeof to.query?.[query_need_hide] === "string") {
      if (isOriginalOpenId(to.query[query_need_hide]))
        to.query[query_need_hide] = base64Encode(to.query[query_need_hide]);
    }
  });

  // compare
  if (!isEqual(to_query, to.query)) {
    return next(to);
  }
  next();
});

router.options.history.listen((_to, _from, info) => {
  is_back_pending.value = info.direction === "back";
});

export default router;
