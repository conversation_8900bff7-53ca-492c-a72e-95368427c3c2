import { UseHomePlateListOptions } from "@/composables/use-home-plate-list";
import { PlatId } from "packages/types/common";

export interface PlateConfig {
  /** useHomePlateList 配置 */
  plateListOptions: UseHomePlateListOptions;
  /** 布局类型 */
  layout?: "normal" | "waterfall";
  /** 是否显示创建入口 */
  showCreateEntrance?: boolean;
  /** 滚动容器额外 class */
  scrollClass?: string;
  /** 是否显示 News 组件 */
  showNews?: boolean;
  /** 是否显示 Header 组件 */
  showHeader?: boolean;
  /** 是否显示 Tags 组件 */
  showTags?: boolean;
  /** 是否显示工具栏 */
  showTools?: boolean;
  /** Header 组件的特殊配置 */
  headerConfig?: {
    showRegion?: boolean;
    showOrderBy?: boolean;
  };
}

/**
 * 各板块的配置
 */
export const PLATE_CONFIGS: Record<string, PlateConfig> = {
  recommend: {
    plateListOptions: {
      defaultOrderBy: 1, // 时间排序
      enableDistrict: true, // 启用金刚区
      enableTags: false, // 不使用标签
    },
    layout: "normal",
    showCreateEntrance: true,
    showNews: true,
    showHeader: true,
    showTags: false,
    showTools: true,
    headerConfig: {
      showRegion: true,
      showOrderBy: true,
    },
  },

  outpost: {
    plateListOptions: {
      defaultOrderBy: 2, // 热度排序
      enableDistrict: true, // 启用金刚区
      enableTags: true, // 启用标签
    },
    layout: "normal",
    showCreateEntrance: true,
    showNews: true,
    showHeader: true,
    showTags: true,
    showTools: true,
    headerConfig: {
      showRegion: true,
      showOrderBy: true,
    },
  },

  guides: {
    plateListOptions: {
      defaultOrderBy: 2, // 热度排序
      enableDistrict: false, // 不启用金刚区
      enableTags: true, // 启用标签
    },
    layout: "normal",
    showCreateEntrance: true,
    showNews: true,
    showHeader: true,
    showTags: true,
    showTools: false,
    headerConfig: {
      showRegion: false,
      showOrderBy: true,
    },
  },

  official: {
    plateListOptions: {
      defaultOrderBy: 1, // 时间排序
      enableDistrict: false, // 不启用金刚区
      enableTags: true, // 启用标签
    },
    layout: "normal",
    showCreateEntrance: true, // 根据用户权限动态控制
    showNews: true,
    showHeader: true,
    showTags: true,
    showTools: false,
    headerConfig: {
      showRegion: false,
      showOrderBy: true,
    },
  },

  nikkeart: {
    plateListOptions: {
      defaultOrderBy: 2, // 热度排序
      enableDistrict: false, // 不启用金刚区
      enableTags: true, // 启用标签
    },
    layout: "waterfall",
    showCreateEntrance: true,
    scrollClass: "w-full relative infinite-scroll-container",
    showNews: true,
    showHeader: true,
    showTags: true,
    showTools: false,
    headerConfig: {
      showRegion: false,
      showOrderBy: true,
    },
  },
};

/**
 * 获取板块配置
 */
export const getPlateConfig = (plateKey: string): PlateConfig => {
  return PLATE_CONFIGS[plateKey] || PLATE_CONFIGS.outpost;
};
