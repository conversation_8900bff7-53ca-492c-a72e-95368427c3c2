<template>
  <div class="w-full">
    <!-- 顶部插槽：用于 News、Header、Tags 等组件 -->
    <slot name="header" :data="headerData" />

    <!-- 创建入口 -->
    <CreateEntrace v-if="showCreateEntrance" @compose="onCompose" />

    <!-- 列表容器 -->
    <InfiniteScroll
      :back_to_top_visible="false"
      :finished_visible="false"
      :loading="postLoading"
      :finished="false"
      :debounce_interval="10"
      :class="scrollClass"
      @load-more="getMore"
    >
      <!-- 瀑布流布局（nikkeart 专用） -->
      <VirtualWaterfall
        v-if="layout === 'waterfall'"
        :items="showList"
        :virtual="false"
        :calc-item-height="calcItemHeight"
        :row-key="'post_uuid'"
      >
        <template #default="{ item, index }">
          <slot name="waterfall-item" :item="item" :index="index" :handlers="itemHandlers">
            <ListItem
              :key="item.post_uuid"
              :index="index"
              :item="item"
              :need_expose="true"
              class="mb-[1px]"
              @star="onStar"
              @detail="onDetail"
              @collection="onCollection"
            />
          </slot>
        </template>
      </VirtualWaterfall>

      <!-- 普通列表布局 -->
      <template v-else-if="showList.length > 0">
        <slot name="list-item" :item="item" :index="index" :handlers="itemHandlers" v-for="(item, index) in showList" :key="item.post_uuid">
          <CardItem
            :item="item"
            :index="index"
            :plate_id="+activeId"
            :plate_name="activeKey"
            :need_expose="true"
            @manage="onManage"
            @share="onShare"
            @star="onStar"
            @follow="onFollow"
            @detail="onDetail"
          />
        </slot>
      </template>

      <!-- 空数据 -->
      <no-data v-if="showList.length === 0 && !postLoading" />
    </InfiniteScroll>

    <!-- 管理弹窗 -->
    <ManageDialog
      :visible="manage_visible"
      :can_edit="cur_item?.can_edit"
      @close="manage_visible = false"
      @delete="onDelete"
      @edit="onEditPost"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

// Components
import CardItem from "@/components/common/card-item/index.vue";
import ManageDialog from "@/components/common/manage-dialog/index.vue";
import NoData from "@/components/common/nodata.vue";
import CreateEntrace from "../createEntrance/index.vue";
import ListItem from "@/components/home/<USER>/index.vue";
import { InfiniteScroll } from "@/components/common/scroll";
import { VirtualWaterfall } from "@lhlyu/vue-virtual-waterfall";

// Composables & Utils
import { useHomePlateList, UseHomePlateListOptions } from "@/composables/use-home-plate-list";
import { useHomeStore } from "@/store/home/<USER>";
import { useFollowUser } from "@/api/user";
import { postStar, useDeletePost, usePostForward, postCollection } from "@/api/post";
import { usePostsStatus } from "@/composables/use-posts-status";
import { useIsDeleted } from "@/composables/use-is-deleted";
import { useDialog } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/toast";

// Types & Constants
import { PostItem, OrderBy } from "packages/types/post";
import { LikeType, StanceType } from "packages/types/stance";
import { PopCallbackValue, PlatId } from "packages/types/common";
import { Routes } from "@/router/routes";
import { itemStatusAndCountHandler } from "packages/utils/tools";
import { report } from "packages/utils/tlog";
import { t } from "@/locales";
import { getStandardizedLang } from "@/utils/lang";

export interface BasePostListProps {
  /** 布局类型：normal 普通列表，waterfall 瀑布流 */
  layout?: 'normal' | 'waterfall';
  /** 是否显示创建入口 */
  showCreateEntrance?: boolean;
  /** 滚动容器的额外 class */
  scrollClass?: string;
  /** useHomePlateList 的配置选项 */
  plateListOptions?: UseHomePlateListOptions;
  /** 瀑布流高度计算函数（仅 waterfall 布局需要） */
  calcItemHeight?: (item: any, itemWidth: number) => number;
}

const props = withDefaults(defineProps<BasePostListProps>(), {
  layout: 'normal',
  showCreateEntrance: true,
  scrollClass: 'w-full relative',
  plateListOptions: () => ({}),
});

// Emits
const emit = defineEmits<{
  orderChange: [value: OrderBy];
  tagChange: [value: string];
  regionChange: [value: any];
}>();

// Composables
const router = useRouter();
const { show: showDialog } = useDialog();
const { show: toast } = useToast();
const { filterIsDeleted, setIsDeletedValue } = useIsDeleted();
const { setPostData } = usePostsStatus();

// Store
const { allNotices, activeId, activeKey } = storeToRefs(useHomeStore());

// 使用 useHomePlateList
const {
  postList,
  postLoading,
  loadMore,
  orderBy,
  changeOrderBy,
  activeTag,
  changeTag,
  tagList,
  districtList,
  awaitPostListLoaded,
} = useHomePlateList(props.plateListOptions);

// 计算属性
const showList = computed(() => filterIsDeleted(postList.value));

const noticeList = computed(() => {
  const target = allNotices.value.find((item) => item.key === activeKey.value);
  return (target?.notices || []).slice(0, 3);
});

// 为 header 插槽提供的数据
const headerData = computed(() => ({
  noticeList: noticeList.value,
  orderBy: orderBy.value,
  activeTag: activeTag.value,
  tagList: tagList.value,
  districtList: districtList.value,
  onOrderChange: (val: OrderBy) => {
    changeOrderBy(val);
    emit('orderChange', val);
  },
  onTagChange: (val: string) => {
    changeTag(val);
    emit('tagChange', val);
  },
  onRegionChange: (val: any) => {
    emit('regionChange', val);
  },
}));

// 状态
const cur_item = ref<PostItem>();
const manage_visible = ref(false);

// 事件处理函数
const onCompose = () => {
  report.standalonesite_news_publish_btn.cm_click();
  router.push({
    path: Routes.POST_COMPOSE,
    query: {
      plate_type: activeKey.value,
    },
  });
};

const getMore = () => {
  loadMore();
};

const onDetail = (item: PostItem) => {
  setPostData(item, postList.value);
};

const onManage = (item: PostItem) => {
  cur_item.value = item;
  manage_visible.value = true;
};

const onShare = async (item: PostItem, channel_name?: string, share_link?: string) => {
  if (channel_name && share_link) {
    report.standalonesite_share_channel_btn.cm_click({
      channel_name,
      url: share_link,
    });
  }

  const { forward_count } = await usePostForward.run({ post_uuid: item.post_uuid });
  item.forward_count = forward_count;
};

const onStar = async (item: PostItem) => {
  await postStar.run({
    post_uuid: item.post_uuid,
    type: StanceType.like,
    like_type: LikeType.like,
  });
  itemStatusAndCountHandler(item, "my_upvote.is_star", "upvote_count");
};

const onFollow = async (item: PostItem) => {
  const newStatus = await useFollowUser.run({ intl_openid: item.user.intl_openid });
  // 这里可以添加事件通知逻辑
};

const onCollection = async (item: PostItem) => {
  await postCollection.run({ post_uuid: item.post_uuid });
  itemStatusAndCountHandler(item, "is_collection", "collection_count");
};

const onDelete = async () => {
  showDialog({
    title: t("delete"),
    content: t("are_you_sure_to_delete"),
    confirm_text: t("confirm"),
    cancel_text: t("close"),
    async callback(options: { value: PopCallbackValue; close: () => void }) {
      const { value, close } = options;
      if (value === PopCallbackValue.confirm) {
        await useDeletePost.run({
          post_uuid: cur_item.value?.post_uuid || "",
        });
        manage_visible.value = false;
        toast({
          text: t("delete_successfully"),
          type: "success",
        });
        setIsDeletedValue(cur_item.value, true);
      }
      close();
    },
  });
};

const onEditPost = () => {
  router.push({
    path: Routes.POST_COMPOSE,
    query: {
      post_uuid: cur_item.value?.post_uuid || "",
      edit_lang: getStandardizedLang(),
    },
  });
};

// 为插槽提供的事件处理器
const itemHandlers = {
  onManage,
  onShare,
  onStar,
  onFollow,
  onDetail,
  onCollection,
};

// 暴露给父组件的方法和数据
defineExpose({
  postList,
  postLoading,
  loadMore,
  orderBy,
  changeOrderBy,
  activeTag,
  changeTag,
  tagList,
  districtList,
  showList,
  headerData,
});
</script>
