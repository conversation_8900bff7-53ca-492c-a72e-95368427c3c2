<template>
  <div class="w-full">
    <!-- 二级tab导航 -->
    <div class="flex items-center h-[44px] bg-[var(--fill-3)] border-b-[1px] border-[var(--line-1)] px-[15px]">
      <div class="flex items-center space-x-[24px]">
        <div
          v-for="tab in subTabs"
          :key="tab.key"
          class="relative cursor-pointer py-[12px]"
          @click="onSubTabChange(tab.key)"
        >
          <span
            class="text-[length:14px] font-medium transition-colors duration-200"
            :class="
              activeSubTab === tab.key
                ? 'text-[color:var(--brand-1)]'
                : 'text-[color:var(--text-2)]'
            "
          >
            {{ tab.label }}
          </span>
          <div
            v-if="activeSubTab === tab.key"
            class="absolute bottom-0 left-0 right-0 h-[2px] bg-[var(--brand-1)] rounded-t-[1px]"
          ></div>
        </div>
      </div>
      
      <!-- 排序选择器 -->
      <div class="ml-auto">
        <DropdownNormal
          :list="orderList"
          :active="orderBy"
          side="bottom"
          align="end"
          @change="onOrderChange"
        >
          <template #trigger="{ item }">
            <SelectHead :text="item.name" />
          </template>
        </DropdownNormal>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="w-full">
      <component :is="activeComponent" :order-by="orderBy" @order-change="onOrderChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useI18n } from "vue-i18n";
import DropdownNormal from "@/components/common/dropdown/index.vue";
import SelectHead from "@/components/common/select-head/index.vue";
import Recommend from "@/components/home/<USER>/index.vue";
import Following from "@/components/home/<USER>/index.vue";
import { OrderBy } from "packages/types/post";
import { report } from "packages/utils/tlog";
import { storeToRefs } from "pinia";
import { useHomeStore } from "@/store/home/<USER>";

const { t } = useI18n();
const { activeId, activeKey } = storeToRefs(useHomeStore());

// 二级tab配置
const subTabs = ref([
  { key: "recommend", label: t("recommend") },
  { key: "following", label: t("following") },
]);

// 当前激活的二级tab
const activeSubTab = ref("recommend");

// 排序选项
const orderList = ref([
  { name: t("latest"), value: 1 },
  { name: t("hot"), value: 2 },
]);

// 当前排序方式，默认最新
const orderBy = ref<OrderBy>(1);

// 当前激活的组件
const activeComponent = computed(() => {
  return {
    recommend: Recommend,
    following: Following,
  }[activeSubTab.value] || Recommend;
});

// 二级tab切换
const onSubTabChange = (key: string) => {
  if (activeSubTab.value === key) return;
  
  activeSubTab.value = key;
  
  // 上报
  report.standalonesite_sub_tab_btn.cm_click({
    label_id: +activeId.value,
    label_name: `${activeKey.value}_${key}`,
  });
};

// 排序切换
const onOrderChange = (val: { name: string; value: number | string }) => {
  const target = orderList.value.find((item) => item.value === val.value)!;
  orderBy.value = val.value as OrderBy;
  
  // 上报
  report.standalonesite_filter_btn.cm_click({
    btn_name: target.name,
    label_id: +activeId.value,
    label_name: `${activeKey.value}_${activeSubTab.value}`,
  });
};
</script>

<style lang="scss" scoped>
.transition-colors {
  transition-property: color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}
</style>
