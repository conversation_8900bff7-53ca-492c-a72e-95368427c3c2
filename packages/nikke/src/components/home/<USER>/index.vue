<template>
  <UniversalPlate plate-key="official" :custom-config="customConfig">
    <!-- 自定义 Header 布局 -->
    <template #header="{ data }">
      <News
        v-if="data.noticeList.length"
        class="px-[15px] h-[55px] pt-[12px]"
        :list="data.noticeList"
      />

      <div class="pt-[12px]">
        <Header :order-by="data.orderBy" @order-change="data.onOrderChange" />
      </div>
    </template>

    <!-- 自定义 Tags 布局 -->
    <template #tags="{ data }">
      <div class="mt-[12px] mx-[15px] relative">
        <Tags
          class="pr-[32px]"
          :list="data.tagList"
          :active-id="data.activeTag"
          @change="data.onTagChange"
        />
      </div>
    </template>
  </UniversalPlate>
</template>

<script setup lang="ts">
import News from "@/components/home/<USER>/index.vue";
import Header from "@/components/home/<USER>/index.vue";
import Tags from "@/components/home/<USER>/index.vue";
import UniversalPlate from "../universal-plate/index.vue";

// 自定义配置，覆盖默认配置
const customConfig = {
  // official 板块的特殊配置可以在这里添加
};
</script>
