<template>
  <div class="w-full">
    <News v-if="noticeList.length" class="px-[15px] h-[55px] pt-[12px]" :list="noticeList"></News>

    <div class="pt-[12px]">
      <Header :order-by="orderBy" @order-change="onOrderChange" />
    </div>

    <div class="mt-[12px] mx-[15px] relative">
      <Tags class="pr-[32px]" :list="tagList" :active-id="activeTag" @change="onTagChange" />
    </div>

    <CreateEntrace v-if="user_store.user_can_publish_multiple_language" @compose="onCompose" />

    <InfiniteScroll
      :back_to_top_visible="false"
      :finished_visible="false"
      :loading="postLoading"
      :finished="false"
      :debounce_interval="10"
      class="w-full relative"
      @load-more="getMore"
    >
      <template v-if="showList.length > 0">
        <CardItem
          v-for="(item, index) in showList"
          :key="index"
          :item="item"
          :index="index"
          :plate_id="+activeId"
          :plate_name="activeKey"
          :need_expose="true"
          @manage="onManage"
          @share="onShare"
          @star="onStar"
          @follow="onFollow"
          @detail="onDetail"
        />
      </template>
      <no-data v-if="showList.length === 0 && !postLoading"></no-data>
    </InfiniteScroll>

    <ManageDialog
      :visible="manage_visible"
      :can_edit="cur_item?.can_edit"
      @close="manage_visible = false"
      @delete="onDelete"
      @edit="onEditPost"
    />
  </div>
</template>

<script setup lang="ts">
import News from "@/components/home/<USER>/index.vue";
import Header from "@/components/home/<USER>/index.vue";
import Tags from "@/components/home/<USER>/index.vue";
import CardItem from "@/components/common/card-item/index.vue";
import ManageDialog from "@/components/common/manage-dialog/index.vue";
import NoData from "@/components/common/nodata.vue";
import CreateEntrace from "../createEntrance/index.vue";
import { useToast } from "@/components/ui/toast";

import { ref, onMounted, computed } from "vue";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import { InfiniteScroll } from "@/components/common/scroll";
import { useHomeStore } from "@/store/home/<USER>";
import { useFollowUser } from "@/api/user";
import { useHomePlateList } from "@/composables/use-home-plate-list";
import { Routes } from "@/router/routes";
import { PostItem, OrderBy } from "packages/types/post";
import { postStar, useDeletePost, usePostForward } from "@/api/post";
import { LikeType, StanceType } from "packages/types/stance";
import { itemStatusAndCountHandler } from "packages/utils/tools";
import { t } from "@/locales";
import { useIsDeleted } from "@/composables/use-is-deleted";
import { useDialog } from "@/components/ui/dialog/index";
import { PlatId, PopCallbackValue } from "packages/types/common";
import { updatePostsData, updateUserStatusInPostList } from "@/utils/home";
import { usePostsStatus } from "@/composables/use-posts-status";
import { report } from "packages/utils/tlog";
import { useUser } from "@/store/user";
import { getStandardizedLang } from "packages/utils/standard";
import { useHomeSubPageLoadTlog } from "@/composables/use-home-tlog";
import { event_emitter, EVENT_NAMES, onEventEmitter } from "packages/utils/event-emitter";

const user_store = useUser();
const { show: showDialog } = useDialog();
const { filterIsDeleted, setIsDeletedValue } = useIsDeleted();
const { show: toast } = useToast();
const router = useRouter();

// 使用新的 composable
const {
  postList,
  postLoading,
  loadMore,
  orderBy,
  changeOrderBy,
  activeTag,
  changeTag,
  tagList,
  awaitPostListLoaded,
} = useHomePlateList({
  defaultOrderBy: 1, // official 默认时间排序
  enableDistrict: false, // 不启用金刚区
  enableTags: true, // 启用标签
});

const { allNotices, activeId, activeKey } = storeToRefs(useHomeStore());
const { setPostData } = usePostsStatus();
const showList = computed(() => filterIsDeleted(postList.value));

const noticeList = computed(() => {
  const target = allNotices.value.find((item) => item.key === PlatId.official);
  return (target?.notices || []).slice(0, 3);
});

const { reportSubPageLoadTime } = useHomeSubPageLoadTlog();

const onCompose = () => {
  report.standalonesite_news_publish_btn.cm_click();
  router.push({
    path: Routes.POST_COMPOSE,
    query: {
      plate_type: activeKey.value,
    },
  });
};

const onOrderChange = (val: OrderBy) => {
  changeOrderBy(val);
};

const getMore = () => {
  loadMore();
};

const cur_item = ref<PostItem>();

const onDetail = (item: PostItem) => {
  setPostData(item, postList.value);
};

const manage_visible = ref(false);
const onManage = (item: PostItem) => {
  cur_item.value = item;
  manage_visible.value = true;
};

const onShare = async (item: PostItem) => {
  const { forward_count } = await usePostForward.run({ post_uuid: item.post_uuid });
  item.forward_count = forward_count;
};

const onStar = async (item: PostItem) => {
  await postStar.run({
    post_uuid: item.post_uuid,
    type: StanceType.like,
    like_type: LikeType.like,
  });
  itemStatusAndCountHandler(item, "my_upvote.is_star", "upvote_count");
};

const onFollow = async (item: PostItem) => {
  const newStatus = await useFollowUser.run({ intl_openid: item.user.intl_openid });
  event_emitter.emit(EVENT_NAMES.user_status_change, {
    intl_openid: item.user.intl_openid,
    is_followed: newStatus.is_follow ? 1 : 0,
    is_mutual_follow: newStatus.is_mutual_follow ? 1 : 0,
  });
  updatePostsData(item, postList.value, newStatus);
};

const onDelete = async () => {
  showDialog({
    title: t("delete"),
    content: t("are_you_sure_to_delete"),
    confirm_text: t("confirm"),
    cancel_text: t("close"),
    async callback(options: { value: PopCallbackValue; close: () => void }) {
      const { value, close } = options;
      if (value === PopCallbackValue.confirm) {
        await useDeletePost.run({
          post_uuid: cur_item.value?.post_uuid || "",
        });
        manage_visible.value = false;
        toast({
          text: t("delete_successfully"),
          type: "success",
        });
        setIsDeletedValue(cur_item.value, true);
      }
      close();
    },
  });
};

const onEditPost = () => {
  router.push({
    path: Routes.POST_COMPOSE,
    query: {
      post_uuid: cur_item.value?.post_uuid || ""!,
      edit_lang: getStandardizedLang(),
    },
  });
};

const onTagChange = (val: string) => {
  changeTag(val);
};

onEventEmitter(EVENT_NAMES.user_status_change, (v) =>
  updateUserStatusInPostList(postList.value, v),
);

onMounted(async () => {
  await awaitPostListLoaded();
  reportSubPageLoadTime();
});
</script>
