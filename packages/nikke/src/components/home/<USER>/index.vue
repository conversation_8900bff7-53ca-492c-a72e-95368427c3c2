<template>
  <UniversalPlate
    plate-key="nikkeart"
    :custom-config="customConfig"
    :calc-item-height="calcItemHeight"
  >
    <!-- 自定义 Header 布局 -->
    <template #header="{ data }">
      <News
        v-if="data.noticeList.length"
        class="px-[15px] h-[55px] pt-[12px]"
        :list="data.noticeList"
      />

      <Header
        class="mt-[12px]"
        :show-region="false"
        :order-by="data.orderBy"
        :all-region="allRegion"
        @order-change="data.onOrderChange"
        @region-change="onRegionChange"
      />
    </template>

    <!-- 自定义 Tags 布局 -->
    <template #tags="{ data }">
      <Tags
        class="mt-[8px] mx-[15px]"
        :list="data.tagList"
        :active-id="data.activeTag"
        @change="data.onTagChange"
      />
    </template>
  </UniversalPlate>
</template>

<script setup lang="ts">
import { h, ref, render } from "vue";
import News from "@/components/home/<USER>/index.vue";
import Header from "@/components/home/<USER>/index.vue";
import Tags from "@/components/home/<USER>/index.vue";
import ListItem from "@/components/home/<USER>/index.vue";
import UniversalPlate from "../universal-plate/index.vue";
import { useHomePlateList } from "@/composables/use-home-plate-list";
import { PostItem } from "packages/types/post";
import { usePostItem } from "@/composables/use-post";
import { report } from "packages/utils/tlog";

// nikkeart 特有的状态
const allRegion = ref(false);

// 获取 reset 方法用于区域切换
const { reset: resetPostList } = useHomePlateList({
  defaultOrderBy: 2,
  enableDistrict: false,
  enableTags: true,
});

const calcItemHeight = (item: PostItem, item_real_width: number) => {
  let dom: HTMLDivElement | null = document.createElement("div");
  Object.assign(dom.style, {
    width: `${item_real_width}px`,
  });

  render(
    h(ListItem, {
      item: item,
    }),
    dom,
  );

  document.body.appendChild(dom);

  let height: number = dom.firstElementChild?.clientHeight || 0;
  const { getPostItemImageDisplaySize } = usePostItem();

  if (!height) {
    const display_size = getPostItemImageDisplaySize(item);
    const ratio = display_size.width / display_size.height;
    height = item_real_width / ratio;
  }

  // 移除
  document.body.removeChild(dom);
  dom = null;

  return height;
};

const onRegionChange = (val: boolean) => {
  allRegion.value = val;
  resetPostList();

  report.standalonesite_all_region_btn.cm_click({ state: val ? 1 : 0 });
};

// 自定义配置，覆盖默认配置
const customConfig = {
  layout: "waterfall" as const,
  scrollClass: "w-full relative infinite-scroll-container",
  // nikkeart 板块的特殊配置可以在这里添加
};
</script>

<style lang="scss">
.infinite-scroll-container {
  background: linear-gradient(to bottom, transparent 200px, var(--fill-4) 300px);
}
</style>
