<template>
  <div class="w-full">
    <BasePostList
      :layout="config.layout"
      :show-create-entrance="showCreateEntrance"
      :scroll-class="config.scrollClass"
      :plate-list-options="config.plateListOptions"
      :calc-item-height="calcItemHeight"
      @order-change="onOrderChange"
      @tag-change="onTagChange"
      @region-change="onRegionChange"
    >
      <!-- Header 插槽 -->
      <template #header="{ data }">
        <!-- News 组件 -->
        <News
          v-if="config.showNews && data.noticeList.length"
          class="px-[15px] h-[55px] pt-[12px]"
          :list="data.noticeList"
        />

        <!-- Tools 组件（仅 recommend 和 outpost） -->
        <Tools v-if="config.showTools" class="mt-[12px]" />

        <!-- Header 组件 -->
        <Header
          v-if="config.showHeader"
          class="mt-[12px]"
          :show-region="config.headerConfig?.showRegion ?? true"
          :order-by="data.orderBy"
          :all-region="allRegion"
          @order-change="data.onOrderChange"
          @region-change="data.onRegionChange"
        />

        <!-- Tags 组件 -->
        <slot name="tags" :data="data">
          <Tags
            v-if="config.showTags"
            class="mt-[8px] mx-[15px]"
            :list="data.tagList"
            :active-id="data.activeTag"
            @change="data.onTagChange"
          />
        </slot>
      </template>

      <!-- 瀑布流项目插槽（nikkeart 专用） -->
      <template #waterfall-item="{ item, index, handlers }" v-if="config.layout === 'waterfall'">
        <ListItem
          :key="item.post_uuid"
          :index="index"
          :item="item"
          :need_expose="true"
          class="mb-[1px]"
          @star="handlers.onStar"
          @detail="handlers.onDetail"
          @collection="handlers.onCollection"
        />
      </template>

      <!-- 普通列表项目插槽 -->
      <template #list-item="{ item, index, handlers }" v-else>
        <CardItem
          :item="item"
          :index="index"
          :plate_id="+activeId"
          :plate_name="activeKey"
          :need_expose="true"
          @manage="handlers.onManage"
          @share="handlers.onShare"
          @star="handlers.onStar"
          @follow="handlers.onFollow"
          @detail="handlers.onDetail"
        />
      </template>
    </BasePostList>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { storeToRefs } from "pinia";

// Components
import BasePostList from "../base-post-list/index.vue";
import News from "@/components/home/<USER>/index.vue";
import Tools from "@/components/home/<USER>/index.vue";
import Header from "@/components/home/<USER>/index.vue";
import Tags from "@/components/home/<USER>/index.vue";
import CardItem from "@/components/common/card-item/index.vue";
import ListItem from "@/components/home/<USER>/index.vue";

// Utils & Types
import { getPlateConfig, PlateConfig } from "../base-post-list/configs";
import { useHomeStore } from "@/store/home/<USER>";
import { useUser } from "@/store/user";
import { OrderBy } from "packages/types/post";

export interface UniversalPlateProps {
  /** 板块标识 */
  plateKey: string;
  /** 自定义配置（可覆盖默认配置） */
  customConfig?: Partial<PlateConfig>;
  /** 瀑布流高度计算函数 */
  calcItemHeight?: (item: any, itemWidth: number) => number;
}

const props = defineProps<UniversalPlateProps>();

// Store
const { activeId, activeKey } = storeToRefs(useHomeStore());
const user_store = useUser();

// 获取配置
const config = computed(() => {
  const baseConfig = getPlateConfig(props.plateKey);
  return { ...baseConfig, ...props.customConfig };
});

// 特殊状态（nikkeart 专用）
const allRegion = ref(false);

// 计算是否显示创建入口
const showCreateEntrance = computed(() => {
  if (props.plateKey === "official") {
    return config.value.showCreateEntrance && user_store.user_can_publish_multiple_language;
  }
  return config.value.showCreateEntrance;
});

// 事件处理
const onOrderChange = (val: OrderBy) => {
  // 可以在这里添加特殊的排序逻辑
  console.log("Order changed:", val);
};

const onTagChange = (val: string) => {
  // 可以在这里添加特殊的标签逻辑
  console.log("Tag changed:", val);
};

const onRegionChange = (val: any) => {
  // nikkeart 特有的区域切换逻辑
  if (props.plateKey === "nikkeart") {
    allRegion.value = val;
  }
  console.log("Region changed:", val);
};
</script>
