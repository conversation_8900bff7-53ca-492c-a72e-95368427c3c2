<template>
  <div class="w-full">
    <!-- 暂时显示一个提示信息，因为没有专门的following feed API -->
    <div class="flex flex-col items-center justify-center py-[60px] px-[20px]">
      <div class="w-[120px] h-[120px] mb-[20px] opacity-30">
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M20.5899 22C20.5899 18.13 16.7399 15 11.9999 15C7.25991 15 3.40991 18.13 3.40991 22"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <h3 class="text-[length:16px] font-medium text-[color:var(--text-1)] mb-[8px]">
        {{ t("following") }}
      </h3>
      <p class="text-[length:14px] text-[color:var(--text-3)] text-center leading-[20px]">
        Coming Soon
      </p>
    </div>

    <!-- 如果有API支持，这里将显示关注用户的帖子列表 -->
    <!-- 
    <InfiniteScroll
      :back_to_top_visible="false"
      :finished_visible="false"
      :loading="postLoading"
      :finished="false"
      :debounce_interval="10"
      class="w-full relative"
      @load-more="getMore"
    >
      <template v-if="showList.length > 0">
        <CardItem
          v-for="(item, index) in showList"
          :key="item.post_uuid"
          :item="item"
          :index="index"
          :plate_id="0"
          plate_name="following"
          :need_expose="true"
          @manage="onManage"
          @share="onShare"
          @star="onStar"
          @follow="onFollow"
          @detail="onDetail"
        />
      </template>
      <no-data v-if="showList.length === 0 && !postLoading"></no-data>
    </InfiniteScroll>
    -->
  </div>
</template>

<script setup lang="ts">
import { defineProps } from "vue";
import { useI18n } from "vue-i18n";
import { OrderBy } from "packages/types/post";

const { t } = useI18n();

// 接收排序参数
defineProps<{
  orderBy: OrderBy;
}>();

// 接收排序变化事件
defineEmits<{
  "order-change": [value: OrderBy];
}>();

// TODO: 当有following feed API时，在这里实现相关逻辑
// 目前暂时显示占位符内容
</script>

<style lang="scss" scoped>
svg {
  color: var(--text-3);
}
</style>
